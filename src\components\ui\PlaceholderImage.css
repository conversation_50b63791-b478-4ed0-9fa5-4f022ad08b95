/**
 * Placeholder Image Component Styles
 * Neo-Brutalism placeholder for portfolio images
 */

.placeholder-image {
  background: linear-gradient(135deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border: 2px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.placeholder-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(45deg, transparent 49%, var(--color-gray-300) 49%, var(--color-gray-300) 51%, transparent 51%),
    linear-gradient(-45deg, transparent 49%, var(--color-gray-300) 49%, var(--color-gray-300) 51%, transparent 51%);
  background-size: 20px 20px;
  opacity: 0.3;
}

.placeholder-content {
  text-align: center;
  z-index: 2;
  position: relative;
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: var(--space-2);
  opacity: 0.6;
}

.placeholder-text {
  font-family: var(--font-display);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  margin-bottom: var(--space-1);
}

.placeholder-dimensions {
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
}
