import { useRef, useState } from 'react';
import { gsap, useGSAP } from '@/utils/gsap';
import BrutalCard from '@/components/ui/BrutalCard';
import BrutalButton from '@/components/ui/BrutalButton';
import ScrollReveal from '@/components/animations/ScrollReveal';
import { sendToWhatsApp, validateFormData, getWhatsAppInfo } from '@/utils/whatsapp';
import './Contact.css';

const Contact = () => {
  const sectionRef = useRef();
  const titleRef = useRef();
  const formRef = useRef();
  const infoRef = useRef();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [errors, setErrors] = useState({});

  const whatsappInfo = getWhatsAppInfo();

  // Form handlers
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleWhatsAppSubmit = () => {
    // Validate form
    const validation = validateFormData(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    // Clear errors and send to WhatsApp
    setErrors({});
    sendToWhatsApp(formData);

    // Reset form
    setFormData({
      name: '',
      email: '',
      message: ''
    });
  };

  useGSAP(() => {
    // Title animation
    gsap.fromTo(titleRef.current, 
      { opacity: 0, y: 60 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

    // Form animation
    gsap.fromTo(formRef.current, 
      { opacity: 0, x: -60 },
      {
        opacity: 1,
        x: 0,
        duration: 0.8,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: formRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

    // Info animation
    gsap.fromTo(infoRef.current, 
      { opacity: 0, x: 60 },
      {
        opacity: 1,
        x: 0,
        duration: 0.8,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: infoRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

  }, { scope: sectionRef });

  return (
    <section ref={sectionRef} id="contact" className="contact">
      <div className="container">
        <div className="contact-header">
          <ScrollReveal animation="fadeUp" delay={0.2}>
            <h2 ref={titleRef} className="section-title">
              <span className="title-label">LET'S</span>
              <span className="title-main">TALK</span>
            </h2>
          </ScrollReveal>
        </div>
        
        <div className="contact-content">
          <ScrollReveal animation="fadeLeft" className="contact-form-wrapper">
            <BrutalCard
              variant="secondary"
              accentColor="var(--color-accent)"
              className="contact-form-card"
            >
              <div className="contact-form-header">
                <h3>GET IN TOUCH</h3>
                <p>Ready to start your next project? Fill out the form below and we'll connect via WhatsApp!</p>
              </div>

              <div className="contact-form">
                <div className="form-group">
                  <label htmlFor="name">NAME *</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={errors.name ? 'error' : ''}
                    placeholder="Your full name"
                    required
                  />
                  {errors.name && <span className="field-error">{errors.name}</span>}
                </div>

                <div className="form-group">
                  <label htmlFor="email">EMAIL *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={errors.email ? 'error' : ''}
                    placeholder="<EMAIL>"
                    required
                  />
                  {errors.email && <span className="field-error">{errors.email}</span>}
                </div>

                <div className="form-group">
                  <label htmlFor="message">MESSAGE *</label>
                  <textarea
                    id="message"
                    name="message"
                    rows="5"
                    value={formData.message}
                    onChange={handleInputChange}
                    className={errors.message ? 'error' : ''}
                    placeholder="Tell us about your project, requirements, timeline, or any questions you have..."
                    required
                  />
                  {errors.message && <span className="field-error">{errors.message}</span>}
                </div>

                <BrutalButton
                  onClick={handleWhatsAppSubmit}
                  variant="primary"
                  size="lg"
                  className="contact-submit"
                  icon="📱"
                >
                  SEND VIA WHATSAPP
                </BrutalButton>
              </div>
            </BrutalCard>
          </ScrollReveal>
          
          <ScrollReveal animation="fadeRight" className="contact-info">
            <BrutalCard
              variant="primary"
              accentColor="var(--color-neon-green)"
              glow={true}
              className="contact-card"
            >
              <h4>QUICK RESPONSE GUARANTEE</h4>
              <p>We respond to all inquiries within {whatsappInfo.responseTime}!</p>
              <p><strong>Business Hours:</strong> {whatsappInfo.businessHours}</p>
              <p><strong>Availability:</strong> {whatsappInfo.availability}</p>
            </BrutalCard>

            <div className="contact-details">
              <BrutalCard variant="secondary" className="contact-item">
                <h5>EMAIL</h5>
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </BrutalCard>

              <BrutalCard variant="secondary" className="contact-item">
                <h5>WHATSAPP</h5>
                <a href={`https://wa.me/${whatsappInfo.number.replace('+', '')}`} target="_blank" rel="noopener noreferrer">
                  {whatsappInfo.displayNumber}
                </a>
              </BrutalCard>

              <BrutalCard variant="secondary" className="contact-item">
                <h5>LOCATION</h5>
                <p>Remote & Global<br />Available Worldwide</p>
              </BrutalCard>

              <BrutalCard
                variant="dark"
                accentColor="var(--color-hot-pink)"
                className="contact-social"
              >
                <h5>FOLLOW US</h5>
                <div className="social-links">
                  <BrutalButton variant="outline" size="sm" href="#" className="social-link">
                    LINKEDIN
                  </BrutalButton>
                  <BrutalButton variant="outline" size="sm" href="#" className="social-link">
                    TWITTER
                  </BrutalButton>
                  <BrutalButton variant="outline" size="sm" href="#" className="social-link">
                    INSTAGRAM
                  </BrutalButton>
                  <BrutalButton variant="outline" size="sm" href="#" className="social-link">
                    GITHUB
                  </BrutalButton>
                </div>
              </BrutalCard>
            </div>
          </ScrollReveal>
        </div>
      </div>
    </section>
  );
};

export default Contact;
