/**
 * Hero Section Styles
 * Neo-Brutalism hero with proper spacing
 */

.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  padding: var(--space-32) 0 var(--space-24);
  margin-top: 80px; /* Account for fixed header */
  overflow: hidden;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(45deg, transparent 49%, var(--color-gray-100) 49%, var(--color-gray-100) 51%, transparent 51%),
    linear-gradient(-45deg, transparent 49%, var(--color-gray-100) 49%, var(--color-gray-100) 51%, transparent 51%);
  background-size: 30px 30px;
  opacity: 0.1;
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--space-16);
  align-items: center;
}

.hero-main {
  max-width: 800px;
}

.hero-title {
  font-family: var(--font-display);
  font-weight: var(--font-weight-black);
  font-size: clamp(3rem, 8vw, 8rem);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tighter);
  color: var(--color-text-primary);
  margin-bottom: var(--space-8);
  text-transform: uppercase;
}

.title-line {
  display: block;
  position: relative;
}

.title-accent {
  color: var(--color-accent);
  position: relative;
}

.title-accent::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 12px;
  background-color: var(--color-neon-green);
  z-index: -1;
  transform: skew(-12deg);
}

.title-small {
  font-size: clamp(1.5rem, 4vw, 3rem);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-secondary);
  margin-top: var(--space-2);
}

.hero-subtitle {
  font-family: var(--font-display);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-6);
  line-height: var(--line-height-snug);
}

.hero-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-10);
  max-width: 600px;
}

.hero-cta {
  display: flex;
  gap: var(--space-6);
  flex-wrap: wrap;
}

.brutal-btn-lg {
  padding: var(--space-5) var(--space-10);
  font-size: var(--font-size-lg);
}

.brutal-btn-secondary {
  background-color: transparent;
  color: var(--color-text-primary);
  border-color: var(--color-border);
}

.brutal-btn-secondary::after {
  background-color: var(--color-gray-200);
}

/* Hero Stats */
.hero-stats {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
  padding: var(--space-8);
  background-color: var(--color-bg-secondary);
  border: 3px solid var(--color-border);
  box-shadow: var(--shadow-brutal-xl) var(--color-shadow);
  transform: rotate(2deg);
}

.stat-item {
  text-align: center;
  padding: var(--space-4);
  background-color: var(--color-bg-primary);
  border: 2px solid var(--color-border);
  box-shadow: var(--shadow-brutal-sm) var(--color-shadow);
  transform: rotate(-1deg);
}

.stat-number {
  font-family: var(--font-display);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-black);
  color: var(--color-accent);
  line-height: 1;
  margin-bottom: var(--space-2);
}

.stat-label {
  font-family: var(--font-display);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
  }
  
  .hero-stats {
    flex-direction: row;
    justify-content: center;
    transform: rotate(0deg);
    max-width: 600px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: var(--space-20) 0 var(--space-16);
    margin-top: 70px;
  }
  
  .hero-title {
    margin-bottom: var(--space-6);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-4);
  }
  
  .hero-description {
    font-size: var(--font-size-base);
    margin-bottom: var(--space-8);
  }
  
  .hero-cta {
    gap: var(--space-4);
    justify-content: center;
  }
  
  .brutal-btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--font-size-base);
  }
  
  .hero-stats {
    flex-direction: column;
    gap: var(--space-4);
    padding: var(--space-6);
  }
  
  .stat-number {
    font-size: var(--font-size-3xl);
  }
}

@media (max-width: 480px) {
  .hero-cta {
    flex-direction: column;
    align-items: center;
  }
  
  .brutal-btn-lg {
    width: 100%;
    max-width: 280px;
    text-align: center;
  }
}
