/**
 * Portfolio Screenshot Capture Script
 * Uses <PERSON><PERSON> to capture full-page screenshots of portfolio sites
 */

import { chromium } from 'playwright';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const portfolioSites = [
  {
    name: 'chalaka-dulanga-photography',
    url: 'https://chalakadulangaphotography.com',
    title: 'Chalaka Dulanga Photography'
  },
  {
    name: 'zeynthra',
    url: 'https://zeynthra.com',
    title: 'Zeynthra Music'
  },
  {
    name: 'spice-herb-restaurant',
    url: 'https://spiceandherbrestaurant.com',
    title: 'Spice & Herb Restaurant'
  }
];

async function capturePortfolioScreenshots() {
  console.log('🚀 Starting portfolio screenshot capture...');
  
  // Create screenshots directory if it doesn't exist
  const screenshotsDir = path.join(__dirname, '..', 'public', 'images', 'portfolio');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }

  // Launch browser
  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 },
    deviceScaleFactor: 1,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  });

  for (const site of portfolioSites) {
    try {
      console.log(`📸 Capturing ${site.title}...`);
      
      const page = await context.newPage();
      
      // Set longer timeout for slow sites
      page.setDefaultTimeout(30000);
      
      // Navigate to the site
      await page.goto(site.url, { 
        waitUntil: 'networkidle',
        timeout: 30000 
      });
      
      // Wait for page to fully load
      await page.waitForTimeout(3000);
      
      // Hide cookie banners and popups if they exist
      await page.evaluate(() => {
        const selectors = [
          '[class*="cookie"]',
          '[class*="gdpr"]',
          '[class*="consent"]',
          '[id*="cookie"]',
          '[id*="gdpr"]',
          '[class*="popup"]',
          '[class*="modal"]'
        ];
        
        selectors.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          elements.forEach(el => {
            if (el.offsetHeight > 0 && el.offsetWidth > 0) {
              el.style.display = 'none';
            }
          });
        });
      });
      
      // Wait a bit more after hiding elements
      await page.waitForTimeout(2000);
      
      // Capture full page screenshot
      const screenshotPath = path.join(screenshotsDir, `${site.name}.png`);
      await page.screenshot({
        path: screenshotPath,
        fullPage: true
      });

      // Also capture a thumbnail version
      const thumbnailPath = path.join(screenshotsDir, `${site.name}-thumb.png`);
      await page.screenshot({
        path: thumbnailPath,
        fullPage: false,
        clip: { x: 0, y: 0, width: 1200, height: 800 }
      });
      
      console.log(`✅ Successfully captured ${site.title}`);
      
      await page.close();
      
    } catch (error) {
      console.error(`❌ Failed to capture ${site.title}:`, error.message);
    }
  }

  await browser.close();
  console.log('🎉 Portfolio screenshot capture completed!');
}

// Optimize images function (basic compression)
async function optimizeImages() {
  console.log('🔧 Optimizing captured images...');

  try {
    const sharp = (await import('sharp')).default;
  const screenshotsDir = path.join(__dirname, '..', 'public', 'images', 'portfolio');
  
  if (!fs.existsSync(screenshotsDir)) {
    console.log('No screenshots directory found');
    return;
  }
  
  const files = fs.readdirSync(screenshotsDir).filter(file => file.endsWith('.png'));
  
  for (const file of files) {
    try {
      const inputPath = path.join(screenshotsDir, file);
      const outputPath = path.join(screenshotsDir, file.replace('.png', '-optimized.webp'));
      
      await sharp(inputPath)
        .webp({ quality: 85, effort: 6 })
        .resize(1200, null, { 
          withoutEnlargement: true,
          fit: 'inside'
        })
        .toFile(outputPath);
        
      console.log(`✅ Optimized ${file} -> ${file.replace('.png', '-optimized.webp')}`);
    } catch (error) {
      console.error(`❌ Failed to optimize ${file}:`, error.message);
    }
  }
  
  console.log('🎉 Image optimization completed!');
  } catch (importError) {
    console.log('📝 Note: Install Sharp for image optimization: npm install sharp');
  }
}

// Main execution
async function main() {
  try {
    await capturePortfolioScreenshots();
    
    // Try to optimize images if Sharp is available
    try {
      await optimizeImages();
    } catch (error) {
      console.log('📝 Note: Install Sharp for image optimization: npm install sharp');
    }
    
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run if called directly
const currentFile = fileURLToPath(import.meta.url);
const executedFile = process.argv[1];

if (currentFile === executedFile) {
  main();
}

export { capturePortfolioScreenshots, optimizeImages };
