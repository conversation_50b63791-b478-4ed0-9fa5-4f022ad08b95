/**
 * Portfolio Section Styles
 * Neo-Brutalism portfolio showcase with proper spacing
 */

.portfolio {
  padding: var(--space-32) 0;
  background-color: var(--color-bg-secondary);
  position: relative;
}

.portfolio::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--color-border) 50%, transparent 100%);
}

.portfolio-header {
  text-align: center;
  margin-bottom: var(--space-20);
}

.section-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  margin-top: var(--space-6);
  font-weight: var(--font-weight-medium);
}

/* Portfolio Grid */
.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-12);
}

.portfolio-card {
  background-color: var(--color-bg-primary);
  border: 3px solid var(--color-border);
  overflow: hidden;
  position: relative;
  transition: var(--transition-base);
  cursor: pointer;
}

.portfolio-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--accent-color);
  transform: translate(12px, 12px);
  z-index: -1;
  transition: var(--transition-base);
}

.portfolio-card:hover {
  transform: translate(6px, 6px);
}

.portfolio-card:hover::before {
  transform: translate(6px, 6px);
}

/* Portfolio Image */
.portfolio-image {
  position: relative;
  height: 250px;
  overflow: hidden;
  background-color: var(--color-gray-200);
}

.portfolio-image img,
.portfolio-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-slow);
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-base);
}

.portfolio-card:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-card:hover .portfolio-image img,
.portfolio-card:hover .portfolio-placeholder {
  transform: scale(1.1);
}

.portfolio-link {
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-display);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--color-secondary);
  background-color: var(--accent-color);
  border: 2px solid var(--accent-color);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  text-decoration: none;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-brutal-sm) var(--color-secondary);
}

.portfolio-link:hover {
  background-color: transparent;
  color: var(--accent-color);
  box-shadow: var(--shadow-brutal-sm) var(--accent-color);
}

/* Portfolio Content */
.portfolio-content {
  padding: var(--space-8);
}

.portfolio-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.portfolio-category {
  font-family: var(--font-display);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.portfolio-accent {
  width: 20px;
  height: 20px;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-brutal-sm) var(--color-shadow);
}

.portfolio-title {
  font-family: var(--font-display);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-tight);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-4);
}

.portfolio-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-6);
}

.portfolio-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-6);
}

.portfolio-tag {
  padding: var(--space-1) var(--space-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.portfolio-btn {
  display: inline-block;
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-display);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: #000000;
  background-color: transparent;
  border: 2px solid var(--color-border);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  text-decoration: none;
  transition: var(--transition-base);
  position: relative;
}

.portfolio-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--accent-color);
  transform: scaleX(0);
  transform-origin: left;
  transition: var(--transition-base);
  z-index: -1;
}

.portfolio-btn:hover::before {
  transform: scaleX(1);
}

.portfolio-btn:hover {
  color: var(--color-secondary);
  border-color: var(--accent-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .portfolio {
    padding: var(--space-24) 0;
  }
  
  .portfolio-header {
    margin-bottom: var(--space-16);
  }
  
  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }
  
  .portfolio-card::before {
    transform: translate(8px, 8px);
  }
  
  .portfolio-card:hover {
    transform: translate(4px, 4px);
  }
  
  .portfolio-card:hover::before {
    transform: translate(4px, 4px);
  }
  
  .portfolio-image {
    height: 200px;
  }
  
  .portfolio-content {
    padding: var(--space-6);
  }
}

@media (max-width: 480px) {
  .portfolio-grid {
    grid-template-columns: 1fr;
  }
  
  .portfolio-title {
    font-size: var(--font-size-xl);
  }
}
