/**
 * Header Component Styles
 * Neo-Brutalism navigation header
 */

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-sticky);
  background-color: var(--color-bg-primary);
  border-bottom: 3px solid var(--color-border);
  box-shadow: 0 4px 0 var(--color-shadow);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) 0;
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-family: var(--font-display);
  font-weight: var(--font-weight-black);
  font-size: var(--font-size-2xl);
  color: #000000;
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-tight);
}

.logo-dot {
  width: 12px;
  height: 12px;
  background-color: var(--color-accent);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-brutal-sm) var(--color-shadow);
}

/* Navigation */
.nav {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.nav-link {
  font-family: var(--font-display);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  transition: var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--color-accent);
  transform: translateY(-2px);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-accent);
  transition: var(--transition-fast);
}

.nav-link:hover::after {
  width: 100%;
}

/* Brutal Button */
.brutal-btn {
  display: inline-block;
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-display);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  border: 3px solid var(--color-border);
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: var(--transition-base);
  position: relative;
  text-decoration: none;
}

.brutal-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-accent);
  transform: translate(4px, 4px);
  z-index: -1;
  transition: var(--transition-base);
}

.brutal-btn:hover {
  transform: translate(2px, 2px);
  color: var(--color-text-primary);
}

.brutal-btn:hover::after {
  transform: translate(2px, 2px);
}

.brutal-btn-primary {
  background-color: var(--color-accent);
  color: var(--color-secondary);
  border-color: var(--color-accent);
}

.brutal-btn-primary::after {
  background-color: var(--color-primary);
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .header-content {
    padding: var(--space-3) 0;
  }
  
  .logo {
    font-size: var(--font-size-xl);
  }
  
  .nav {
    gap: var(--space-4);
  }
  
  .nav-link {
    font-size: var(--font-size-xs);
  }
  
  .brutal-btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .nav {
    gap: var(--space-2);
  }
  
  .nav-link:not(.brutal-btn) {
    display: none;
  }
}
