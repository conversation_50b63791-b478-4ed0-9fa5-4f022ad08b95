/**
 * GSAP Configuration and Setup
 * Centralized GSAP configuration for the Tera Works portfolio
 */

import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { useGSAP } from '@gsap/react';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, useGSAP);

// Global GSAP configuration
gsap.config({
  force3D: true,
  nullTargetWarn: false,
});

// Default animation settings for consistency
export const defaultEase = 'power2.out';
export const defaultDuration = 0.8;

// Common animation presets for Neo-Brutalism style
export const animations = {
  // Entrance animations
  fadeInUp: {
    y: 60,
    opacity: 0,
    duration: defaultDuration,
    ease: defaultEase,
  },
  
  fadeInLeft: {
    x: -60,
    opacity: 0,
    duration: defaultDuration,
    ease: defaultEase,
  },
  
  fadeInRight: {
    x: 60,
    opacity: 0,
    duration: defaultDuration,
    ease: defaultEase,
  },
  
  // Scale animations for brutalist impact
  scaleIn: {
    scale: 0.8,
    opacity: 0,
    duration: defaultDuration,
    ease: 'back.out(1.7)',
  },
  
  // Rotation for dynamic elements
  rotateIn: {
    rotation: -10,
    scale: 0.9,
    opacity: 0,
    duration: defaultDuration,
    ease: defaultEase,
  },
  
  // Stagger settings for multiple elements
  stagger: {
    amount: 0.3,
    from: 'start',
  },
};

// ScrollTrigger defaults for consistent behavior
export const scrollTriggerDefaults = {
  start: 'top 80%',
  end: 'bottom 20%',
  toggleActions: 'play none none reverse',
};

// Utility function to create consistent scroll animations
export const createScrollAnimation = (element, animation, options = {}) => {
  return gsap.fromTo(element, animation, {
    ...animation,
    scrollTrigger: {
      trigger: element,
      ...scrollTriggerDefaults,
      ...options.scrollTrigger,
    },
    ...options,
  });
};

// Refresh ScrollTrigger (useful for dynamic content)
export const refreshScrollTrigger = () => {
  ScrollTrigger.refresh();
};

// Kill all ScrollTriggers (cleanup utility)
export const killAllScrollTriggers = () => {
  ScrollTrigger.killAll();
};

export { gsap, ScrollTrigger, useGSAP };
