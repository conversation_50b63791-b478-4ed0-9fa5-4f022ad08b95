/**
 * ScrollReveal Animation Styles
 * Additional CSS for scroll-triggered animations
 */

.scroll-reveal {
  will-change: transform, opacity;
}

/* Typewriter cursor animation */
.cursor {
  animation: blink 1s infinite;
  color: var(--color-accent);
  font-weight: bold;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* Morphing animation support */
.scroll-reveal.morphing {
  transform-origin: center;
}

/* Stagger animation containers */
.scroll-reveal.stagger > * {
  opacity: 0;
  transform: translateY(20px);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .scroll-reveal {
    animation: none !important;
    transition: none !important;
  }
  
  .cursor {
    animation: none;
    opacity: 1;
  }
}
