# Tera Works Portfolio - Project Structure

## 📁 Directory Structure

```
tera-works-portfolio/
├── public/                     # Static assets
│   └── vite.svg               # Vite logo
├── src/                       # Source code
│   ├── components/            # React components
│   │   ├── layout/           # Layout components (<PERSON><PERSON>, Footer, etc.)
│   │   ├── ui/               # Reusable UI components (<PERSON><PERSON>, Card, etc.)
│   │   ├── portfolio/        # Portfolio-specific components
│   │   └── animations/       # GSAP animation components
│   ├── styles/               # CSS and styling
│   │   ├── reset.css         # Modern CSS reset + utilities
│   │   └── theme.css         # Neo-Brutalism theme system
│   ├── assets/               # Images, fonts, static assets
│   │   ├── images/           # Image files
│   │   └── fonts/            # Custom fonts
│   ├── hooks/                # Custom React hooks
│   ├── utils/                # Utility functions
│   │   └── gsap.js           # GSAP configuration and utilities
│   ├── data/                 # Static data and content
│   ├── App.jsx               # Main App component
│   ├── App.css               # App-specific styles
│   ├── main.jsx              # React entry point
│   └── index.css             # Global styles
├── eslint.config.js          # ESLint configuration
├── vite.config.js            # Vite configuration
├── package.json              # Dependencies and scripts
├── README.md                 # Project documentation
└── PROJECT_STRUCTURE.md      # This file
```

## 🛠️ Configuration Files

### package.json
- **Dependencies**: React 19, GSAP, @gsap/react
- **Dev Dependencies**: Vite, ESLint, TypeScript types
- **Scripts**: dev, build, lint, preview, clean

### vite.config.js
- **Aliases**: Path aliases for clean imports (@, @components, etc.)
- **Build Optimization**: Code splitting for vendor and GSAP chunks
- **Dev Server**: Port 3000 with auto-open

### eslint.config.js
- **Modern ESLint**: Flat config with React hooks support
- **Quality Rules**: Strict linting for code consistency
- **React Refresh**: Hot reload support

## 🎨 Design System

### CSS Architecture
- **reset.css**: Modern CSS reset with accessibility features
- **theme.css**: Complete Neo-Brutalism design system
- **index.css**: Global styles and imports

### Design Tokens
- **Colors**: Primary, secondary, accent, and semantic colors
- **Typography**: Font scales, weights, and line heights
- **Spacing**: Consistent spacing scale
- **Shadows**: Brutalist shadow system
- **Breakpoints**: Responsive design breakpoints

### Typography System
- **Font Family**: Inter for clean, modern typography
- **Display Classes**: .text-display-1 through .text-display-3
- **Heading Classes**: .text-h1 through .text-h6
- **Body Classes**: .text-body-lg, .text-body, .text-body-sm
- **Utility Classes**: .text-caption, color utilities

## 🎯 GSAP Integration

### utils/gsap.js
- **Plugin Registration**: ScrollTrigger, useGSAP hook
- **Animation Presets**: Common Neo-Brutalism animations
- **Utility Functions**: Scroll animations, cleanup helpers
- **Default Settings**: Consistent easing and duration

### Animation System
- **Entrance Animations**: fadeInUp, fadeInLeft, scaleIn, rotateIn
- **Scroll Triggers**: Consistent scroll-based animations
- **Stagger Effects**: Coordinated multi-element animations
- **Performance**: Optimized with force3D and proper cleanup

## 🚀 Development Workflow

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Check code quality
npm run lint:fix     # Fix linting issues
npm run clean        # Clean build directory
```

### Code Quality
- **ESLint**: Strict linting rules for consistency
- **Modern JavaScript**: ES6+ features and best practices
- **React Best Practices**: Hooks, functional components
- **Accessibility**: WCAG compliant design patterns

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 640px
- **Tablet**: 640px - 1024px
- **Desktop**: 1024px - 1280px
- **Large Desktop**: > 1280px

### Mobile-First Approach
- Base styles for mobile
- Progressive enhancement for larger screens
- Touch-friendly interactions
- Optimized typography scaling

## 🎨 Neo-Brutalism Features

### Design Principles
- **Bold Typography**: Large, impactful text as design elements
- **High Contrast**: Strong color contrasts for visual impact
- **Geometric Shapes**: Unconventional layouts and forms
- **Raw Aesthetics**: Honest, unpolished digital materials
- **Strategic Shadows**: 3D effects with brutal shadow system
- **Asymmetric Layouts**: Intentionally unbalanced compositions

### Implementation
- **CSS Custom Properties**: Consistent design tokens
- **Utility Classes**: Reusable styling components
- **Component Architecture**: Modular, maintainable code
- **Animation Integration**: Smooth GSAP-powered interactions

## 🎯 Completed Features

### Enhanced UI Components
- **BrutalButton**: Interactive buttons with GSAP animations, hover effects, and click sounds
- **BrutalCard**: 3D tilt effects, glow animations, and multiple variants
- **ScrollReveal**: Advanced scroll-triggered animations with 10+ animation types
- **Audio Integration**: Click sound effects on all interactive elements
- **WhatsApp Integration**: Direct form submission to WhatsApp with validation

### Layout Components
- **Header**: Fixed navigation with enhanced brutal buttons
- **Hero**: Multi-layered animations with staggered reveals and glowing stat cards
- **Services**: 6-card service grid with tilt effects and scroll animations
- **Portfolio**: Real portfolio screenshots with enhanced hover interactions
- **Contact**: WhatsApp-integrated form with validation and enhanced UI cards
- **Footer**: Multi-column footer with animated brutal button links

### Advanced Animations
- **GSAP ScrollTrigger**: Comprehensive scroll-based animations
- **Framer Motion**: Smooth entrance and interaction animations
- **Stagger Effects**: Sequential animations for card grids
- **3D Tilt Effects**: Mouse-following card rotations
- **Glow Effects**: Dynamic accent-colored glows on hover
- **Micro-interactions**: Button press animations and hover states

### Portfolio Content
- **Real Screenshots**: Captured from actual portfolio sites using Playwright
- **Image Optimization**: Automated screenshot capture and optimization
- **Performance**: Lazy loading and optimized image delivery

### Technical Features
- **Audio System**: Click sound management with volume control
- **Form Validation**: Real-time validation with error states
- **WhatsApp API**: Direct message formatting and sending
- **Responsive Design**: Enhanced mobile interactions and touch targets
- **Accessibility**: WCAG compliant with reduced motion support
- **Performance**: Optimized animations and efficient rendering

### Quality Assurance
- **Comprehensive Testing**: Automated functionality testing with Playwright
- **Cross-browser Compatibility**: Tested audio and animation features
- **Performance Monitoring**: Load time and interaction metrics
- **Code Quality**: ESLint compliance with modern best practices

---

**Status**: ✅ Enhanced Neo-Brutalism Portfolio Complete with Advanced Interactivity
**Next Phase**: Production Deployment & Performance Optimization
