import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { gsap, useGSAP } from '@/utils/gsap';
import './ScrollReveal.css';

const ScrollReveal = ({ 
  children, 
  animation = 'fadeUp',
  delay = 0,
  duration = 0.8,
  distance = 60,
  stagger = 0,
  triggerOnce = true,
  className = '',
  ...props 
}) => {
  const ref = useRef();
  const isInView = useInView(ref, { 
    once: triggerOnce,
    margin: '-10% 0px -10% 0px'
  });

  // Animation variants
  const animations = {
    fadeUp: {
      hidden: { opacity: 0, y: distance },
      visible: { opacity: 1, y: 0 }
    },
    fadeDown: {
      hidden: { opacity: 0, y: -distance },
      visible: { opacity: 1, y: 0 }
    },
    fadeLeft: {
      hidden: { opacity: 0, x: distance },
      visible: { opacity: 1, x: 0 }
    },
    fadeRight: {
      hidden: { opacity: 0, x: -distance },
      visible: { opacity: 1, x: 0 }
    },
    scale: {
      hidden: { opacity: 0, scale: 0.8 },
      visible: { opacity: 1, scale: 1 }
    },
    rotate: {
      hidden: { opacity: 0, rotate: -10, scale: 0.9 },
      visible: { opacity: 1, rotate: 0, scale: 1 }
    },
    flip: {
      hidden: { opacity: 0, rotateX: -90 },
      visible: { opacity: 1, rotateX: 0 }
    },
    slide: {
      hidden: { opacity: 0, x: -100, skewX: 10 },
      visible: { opacity: 1, x: 0, skewX: 0 }
    },
    bounce: {
      hidden: { opacity: 0, y: distance, scale: 0.3 },
      visible: { 
        opacity: 1, 
        y: 0, 
        scale: 1,
        transition: {
          type: "spring",
          damping: 10,
          stiffness: 100
        }
      }
    },
    elastic: {
      hidden: { opacity: 0, scale: 0, rotate: 180 },
      visible: { 
        opacity: 1, 
        scale: 1, 
        rotate: 0,
        transition: {
          type: "spring",
          damping: 15,
          stiffness: 300
        }
      }
    }
  };

  const selectedAnimation = animations[animation] || animations.fadeUp;

  // GSAP enhanced animations for complex effects
  useGSAP(() => {
    if (!ref.current) return;

    const element = ref.current;
    const children = element.children;

    if (animation === 'stagger' && children.length > 0) {
      gsap.fromTo(children, 
        { 
          opacity: 0, 
          y: distance,
          scale: 0.9
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration,
          stagger: stagger || 0.1,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: element,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse',
          }
        }
      );
    }

    if (animation === 'typewriter') {
      const text = element.textContent;
      element.innerHTML = '';
      
      gsap.to({}, {
        duration,
        ease: 'none',
        onUpdate() {
          const progress = this.progress();
          const currentLength = Math.floor(progress * text.length);
          element.innerHTML = text.slice(0, currentLength) +
            (progress < 1 ? '<span class="cursor">|</span>' : '');
        },
        scrollTrigger: {
          trigger: element,
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        }
      });
    }

    if (animation === 'morphing') {
      gsap.fromTo(element,
        {
          opacity: 0,
          scale: 0.5,
          borderRadius: '50%',
          rotation: 180
        },
        {
          opacity: 1,
          scale: 1,
          borderRadius: '0%',
          rotation: 0,
          duration,
          ease: 'elastic.out(1, 0.5)',
          scrollTrigger: {
            trigger: element,
            start: 'top 80%',
            toggleActions: 'play none none reverse',
          }
        }
      );
    }

  }, { scope: ref });

  return (
    <motion.div
      ref={ref}
      className={`scroll-reveal ${className}`}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={selectedAnimation}
      transition={{
        duration,
        delay,
        ease: [0.25, 0.46, 0.45, 0.94]
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export default ScrollReveal;
