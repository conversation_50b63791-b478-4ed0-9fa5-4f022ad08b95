/**
 * Enhanced Neo-Brutalism Button Component
 * Advanced interactive button with GSAP animations and sound effects
 */

.brutal-button-container {
  position: relative;
  display: inline-block;
}

.brutal-button-link {
  text-decoration: none;
  color: inherit;
}

.brutal-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-display);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  border: 3px solid var(--color-border);
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary) !important;
  cursor: pointer;
  transition: none;
  user-select: none;
  overflow: hidden;
  z-index: 2;
}

.brutal-button-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-primary);
  z-index: 1;
}

.brutal-button-content {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  position: relative;
  z-index: 3;
}

.brutal-button-icon {
  display: flex;
  align-items: center;
  font-size: 1.2em;
}

.brutal-button-text {
  white-space: nowrap;
  color: #FFFFFF !important;
}

.brutal-button-glitch {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--color-accent) 50%,
    transparent 100%
  );
  transform: translateX(-100%);
  opacity: 0;
  z-index: 1;
}

/* Button Variants */
.brutal-button--primary {
  background-color: var(--color-accent) !important;
  color: var(--color-secondary) !important;
  border-color: var(--color-accent);
}

.brutal-button--primary .brutal-button-shadow {
  background-color: var(--color-primary);
}

.brutal-button--primary:hover {
  background-color: var(--color-accent-dark) !important;
  color: var(--color-secondary) !important;
}

.brutal-button--primary:hover .brutal-button-glitch {
  animation: glitch-sweep 0.6s ease-out;
}

.brutal-button--secondary {
  background-color: var(--color-bg-primary) !important;
  color: var(--color-text-primary) !important;
  border-color: var(--color-border);
}

.brutal-button--secondary:hover {
  background-color: var(--color-gray-100) !important;
  color: var(--color-text-primary) !important;
}

.brutal-button--secondary .brutal-button-shadow {
  background-color: var(--color-gray-400);
}

.brutal-button--secondary:hover .brutal-button-glitch {
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--color-neon-green) 50%,
    transparent 100%
  );
  animation: glitch-sweep 0.6s ease-out;
}

.brutal-button--outline {
  background-color: transparent !important;
  color: var(--color-text-primary) !important;
  border-color: var(--color-border);
}

.brutal-button--outline .brutal-button-shadow {
  background-color: var(--color-border);
}

.brutal-button--outline:hover {
  background-color: var(--color-text-primary) !important;
  color: var(--color-bg-primary) !important;
}

.brutal-button--outline:hover .brutal-button-glitch {
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--color-secondary) 50%,
    transparent 100%
  );
  animation: glitch-sweep 0.6s ease-out;
}

.brutal-button--danger {
  background-color: var(--color-error);
  color: var(--color-secondary);
  border-color: var(--color-error);
}

.brutal-button--danger .brutal-button-shadow {
  background-color: var(--color-primary);
}

.brutal-button--success {
  background-color: var(--color-success);
  color: var(--color-primary);
  border-color: var(--color-success);
}

.brutal-button--success .brutal-button-shadow {
  background-color: var(--color-primary);
}

/* Button Sizes */
.brutal-button--sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
}

.brutal-button--md {
  padding: var(--space-3) var(--space-6);
  font-size: var(--font-size-base);
}

.brutal-button--lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-lg);
}

.brutal-button--xl {
  padding: var(--space-5) var(--space-10);
  font-size: var(--font-size-xl);
}

/* Disabled State */
.brutal-button--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.brutal-button--disabled .brutal-button-shadow {
  background-color: var(--color-gray-300);
}

/* Animations */
@keyframes glitch-sweep {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Text Contrast Classes */
.brutal-button-light-text {
  color: var(--color-secondary) !important;
}

.brutal-button-dark-text {
  color: var(--color-text-primary) !important;
}

.brutal-button-accent-text {
  color: var(--color-accent) !important;
}

/* High Contrast Variants */
.brutal-button--high-contrast {
  background-color: var(--color-primary);
  color: var(--color-secondary);
  border-color: var(--color-primary);
}

.brutal-button--high-contrast .brutal-button-shadow {
  background-color: var(--color-accent);
}

.brutal-button--high-contrast:hover {
  background-color: var(--color-gray-800);
  color: var(--color-secondary);
}

/* Hover Effects */
.brutal-button:hover .brutal-button-glitch {
  animation: glitch-sweep 0.6s ease-out;
}

/* Focus States for Accessibility */
.brutal-button:focus-visible {
  outline: 3px solid var(--color-accent);
  outline-offset: 2px;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .brutal-button {
    min-height: 44px; /* Touch target size */
  }
  
  .brutal-button--lg {
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-base);
  }
  
  .brutal-button--xl {
    padding: var(--space-4) var(--space-8);
    font-size: var(--font-size-lg);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .brutal-button-glitch {
    animation: none !important;
  }
  
  .brutal-button {
    transition: opacity 0.2s ease;
  }
}

.brutal-button-container .start-project-button .brutal-button-text {
  color: #000000 !important;
}

.brutal-button-container .start-project-button .brutal-button-icon {
  color: #000000;
}
