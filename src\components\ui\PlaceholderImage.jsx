import './PlaceholderImage.css';

const PlaceholderImage = ({ width = 400, height = 300, title, className = '' }) => {
  return (
    <div 
      className={`placeholder-image ${className}`}
      style={{ width: `${width}px`, height: `${height}px` }}
    >
      <div className="placeholder-content">
        <div className="placeholder-icon">📸</div>
        <div className="placeholder-text">{title || 'Portfolio Image'}</div>
        <div className="placeholder-dimensions">{width} × {height}</div>
      </div>
    </div>
  );
};

export default PlaceholderImage;
