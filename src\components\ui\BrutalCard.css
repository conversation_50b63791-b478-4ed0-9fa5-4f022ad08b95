/**
 * Enhanced Neo-Brutalism Card Component
 * Advanced interactive card with 3D effects and animations
 */

.brutal-card-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.brutal-card {
  position: relative;
  background-color: var(--color-bg-primary);
  border: 3px solid var(--color-border);
  padding: var(--space-6);
  z-index: 2;
  transition: none;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.brutal-card-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-primary);
  z-index: 1;
  transform: translate(6px, 6px);
}

.brutal-card-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background-color: var(--accent-color, var(--color-accent));
  opacity: 0;
  filter: blur(20px);
  z-index: 0;
  border-radius: 8px;
}

.brutal-card-content {
  position: relative;
  z-index: 3;
}

.brutal-card-border-accent {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--accent-color, var(--color-accent));
  z-index: 4;
}

/* Card Variants */
.brutal-card--default {
  background-color: var(--color-bg-primary);
  border-color: var(--color-border);
}

.brutal-card--primary {
  background-color: var(--color-accent);
  color: var(--color-secondary);
  border-color: var(--color-accent);
}

.brutal-card--primary .brutal-card-shadow {
  background-color: var(--color-primary);
}

.brutal-card--secondary {
  background-color: var(--color-bg-secondary);
  border-color: var(--color-border);
}

.brutal-card--secondary .brutal-card-shadow {
  background-color: var(--color-gray-400);
}

.brutal-card--dark {
  background-color: var(--color-primary);
  color: var(--color-secondary);
  border-color: var(--color-primary);
}

.brutal-card--dark .brutal-card-shadow {
  background-color: var(--color-gray-600);
}

.brutal-card--outline {
  background-color: transparent;
  border-color: var(--color-border);
}

.brutal-card--outline .brutal-card-shadow {
  background-color: var(--color-border);
}

.brutal-card--gradient {
  background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-neon-green) 100%);
  color: var(--color-primary);
  border-color: var(--color-accent);
}

.brutal-card--gradient .brutal-card-shadow {
  background-color: var(--color-primary);
}

/* Interactive States */
.brutal-card--clickable {
  cursor: pointer;
}

.brutal-card--clickable:hover {
  transform: translateZ(10px);
}

/* Hover Effects */
.brutal-card:hover .brutal-card-border-accent {
  animation: accent-pulse 1s ease-in-out infinite alternate;
}

@keyframes accent-pulse {
  0% {
    height: 4px;
    opacity: 1;
  }
  100% {
    height: 8px;
    opacity: 0.8;
  }
}

/* Special Effects */
.brutal-card--glitch {
  position: relative;
  overflow: hidden;
}

.brutal-card--glitch::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  z-index: 5;
  transition: left 0.5s ease;
}

.brutal-card--glitch:hover::before {
  left: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .brutal-card {
    padding: var(--space-4);
  }
  
  .brutal-card-shadow {
    transform: translate(4px, 4px);
  }
  
  .brutal-card-glow {
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    filter: blur(10px);
  }
}

/* Accessibility */
.brutal-card:focus-visible {
  outline: 3px solid var(--color-accent);
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .brutal-card {
    transform: none !important;
  }
  
  .brutal-card-shadow {
    transform: translate(6px, 6px) !important;
  }
  
  .brutal-card-border-accent {
    animation: none !important;
  }
  
  .brutal-card--glitch::before {
    transition: none;
  }
}

/* Loading State */
.brutal-card--loading {
  position: relative;
  overflow: hidden;
}

.brutal-card--loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--color-accent) 50%,
    transparent 100%
  );
  animation: loading-sweep 1.5s ease-in-out infinite;
  z-index: 6;
}

@keyframes loading-sweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Nested Card Styles */
.brutal-card .brutal-card {
  margin: var(--space-4) 0;
  border-width: 2px;
}

.brutal-card .brutal-card .brutal-card-shadow {
  transform: translate(3px, 3px);
}
