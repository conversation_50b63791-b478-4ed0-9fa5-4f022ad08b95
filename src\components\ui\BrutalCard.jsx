import { useRef } from 'react';
import { gsap, useGSAP } from '@/utils/gsap';
import './BrutalCard.css';

const BrutalCard = ({ 
  children, 
  variant = 'default',
  hover = true,
  tilt = true,
  glow = false,
  className = '',
  onClick,
  accentColor,
  ...props 
}) => {
  const cardRef = useRef();
  const shadowRef = useRef();
  const glowRef = useRef();

  useGSAP(() => {
    const card = cardRef.current;
    const shadow = shadowRef.current;
    const glowElement = glowRef.current;

    if (!card || !hover) return;

    const handleMouseEnter = () => {
      gsap.to(card, {
        y: -8,
        x: -4,
        duration: 0.4,
        ease: 'power2.out',
      });
      
      gsap.to(shadow, {
        y: 8,
        x: 4,
        scale: 1.02,
        duration: 0.4,
        ease: 'power2.out',
      });

      if (glow && glowElement) {
        gsap.to(glowElement, {
          opacity: 0.6,
          scale: 1.05,
          duration: 0.4,
          ease: 'power2.out',
        });
      }
    };

    const handleMouseLeave = () => {
      gsap.to(card, {
        y: 0,
        x: 0,
        rotation: 0,
        duration: 0.6,
        ease: 'elastic.out(1, 0.5)',
      });
      
      gsap.to(shadow, {
        y: 0,
        x: 0,
        scale: 1,
        duration: 0.6,
        ease: 'elastic.out(1, 0.5)',
      });

      if (glow && glowElement) {
        gsap.to(glowElement, {
          opacity: 0,
          scale: 1,
          duration: 0.4,
          ease: 'power2.out',
        });
      }
    };

    const handleMouseMove = (e) => {
      if (!tilt) return;
      
      const rect = card.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const deltaX = (e.clientX - centerX) / (rect.width / 2);
      const deltaY = (e.clientY - centerY) / (rect.height / 2);
      
      const rotateX = deltaY * -10;
      const rotateY = deltaX * 10;
      
      gsap.to(card, {
        rotationX: rotateX,
        rotationY: rotateY,
        duration: 0.3,
        ease: 'power2.out',
        transformPerspective: 1000,
      });
    };

    card.addEventListener('mouseenter', handleMouseEnter);
    card.addEventListener('mouseleave', handleMouseLeave);
    card.addEventListener('mousemove', handleMouseMove);

    return () => {
      card.removeEventListener('mouseenter', handleMouseEnter);
      card.removeEventListener('mouseleave', handleMouseLeave);
      card.removeEventListener('mousemove', handleMouseMove);
    };
  }, { scope: cardRef });

  const cardClasses = `
    brutal-card 
    brutal-card--${variant}
    ${onClick ? 'brutal-card--clickable' : ''}
    ${className}
  `.trim();

  const cardStyle = accentColor ? { '--accent-color': accentColor } : {};

  return (
    <div className="brutal-card-container" style={cardStyle}>
      {glow && (
        <div 
          ref={glowRef} 
          className="brutal-card-glow"
          style={{ backgroundColor: accentColor || 'var(--color-accent)' }}
        />
      )}
      <div ref={shadowRef} className="brutal-card-shadow"></div>
      <div
        ref={cardRef}
        className={cardClasses}
        onClick={onClick}
        {...props}
      >
        <div className="brutal-card-content">
          {children}
        </div>
        <div className="brutal-card-border-accent"></div>
      </div>
    </div>
  );
};

export default BrutalCard;
