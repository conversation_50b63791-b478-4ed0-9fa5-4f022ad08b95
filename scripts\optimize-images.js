import fs from 'fs';
import path from 'path';

async function optimizeImages() {
  console.log('🖼️ Optimizing portfolio images...');
  
  const portfolioDir = 'public/images/portfolio';
  
  try {
    // Check current sizes
    console.log('\n📊 Current image sizes:');
    const files = fs.readdirSync(portfolioDir);
    let totalSize = 0;
    
    files.forEach(file => {
      const filePath = path.join(portfolioDir, file);
      const stats = fs.statSync(filePath);
      const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
      console.log(`  ${file}: ${sizeInMB}MB`);
      totalSize += stats.size;
    });
    
    console.log(`\n📈 Total portfolio images size: ${(totalSize / (1024 * 1024)).toFixed(2)}MB`);
    
    // For this optimization, we'll remove the large full-size images and keep only thumbnails
    // This is a practical approach for GitHub deployment
    
    console.log('\n🔧 Optimization strategy:');
    console.log('  • Keep thumbnail images (smaller, optimized)');
    console.log('  • Remove large full-size images');
    console.log('  • Update portfolio data to use thumbnails');
    
    // Remove large images, keep thumbnails
    const imagesToRemove = [
      'chalaka-dulanga-photography.png',
      'spice-herb-restaurant.png'
    ];
    
    let removedSize = 0;
    imagesToRemove.forEach(file => {
      const filePath = path.join(portfolioDir, file);
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        removedSize += stats.size;
        fs.unlinkSync(filePath);
        console.log(`✅ Removed: ${file} (${(stats.size / (1024 * 1024)).toFixed(2)}MB)`);
      }
    });
    
    // Check new sizes
    console.log('\n📊 Optimized image sizes:');
    const remainingFiles = fs.readdirSync(portfolioDir);
    let newTotalSize = 0;
    
    remainingFiles.forEach(file => {
      const filePath = path.join(portfolioDir, file);
      const stats = fs.statSync(filePath);
      const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
      console.log(`  ${file}: ${sizeInMB}MB`);
      newTotalSize += stats.size;
    });
    
    console.log(`\n📉 New total portfolio images size: ${(newTotalSize / (1024 * 1024)).toFixed(2)}MB`);
    console.log(`💾 Space saved: ${(removedSize / (1024 * 1024)).toFixed(2)}MB`);
    
    return {
      originalSize: totalSize,
      newSize: newTotalSize,
      savedSize: removedSize
    };
    
  } catch (error) {
    console.error('❌ Image optimization failed:', error.message);
    return null;
  }
}

optimizeImages().then(result => {
  if (result) {
    console.log('\n🎉 Image optimization complete!');
    console.log(`📊 Reduction: ${((result.savedSize / result.originalSize) * 100).toFixed(1)}%`);
  }
}).catch(console.error);
