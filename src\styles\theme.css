/**
 * Neo-Brutalism Theme System
 * Color palette and typography for Tera Works portfolio
 */

:root {
  /* Neo-Brutalism Color Palette */
  
  /* Primary Colors - Bold and Impactful */
  --color-primary: #000000;
  --color-primary-light: #333333;
  --color-primary-dark: #000000;
  
  /* Secondary Colors - High Contrast */
  --color-secondary: #ffffff;
  --color-secondary-light: #f8f8f8;
  --color-secondary-dark: #e5e5e5;
  
  /* Accent Colors - Vibrant and Loud */
  --color-accent: #ff0066;
  --color-accent-light: #ff3385;
  --color-accent-dark: #cc0052;
  
  /* Additional Brutalist Colors */
  --color-electric-blue: #0066ff;
  --color-neon-green: #00ff66;
  --color-cyber-yellow: #ffff00;
  --color-hot-pink: #ff0099;
  --color-brutal-orange: #ff6600;
  
  /* Neutral Colors */
  --color-gray-50: #fafafa;
  --color-gray-100: #f5f5f5;
  --color-gray-200: #e5e5e5;
  --color-gray-300: #d4d4d4;
  --color-gray-400: #a3a3a3;
  --color-gray-500: #737373;
  --color-gray-600: #525252;
  --color-gray-700: #404040;
  --color-gray-800: #262626;
  --color-gray-900: #171717;
  
  /* Semantic Colors */
  --color-success: var(--color-neon-green);
  --color-warning: var(--color-cyber-yellow);
  --color-error: var(--color-accent);
  --color-info: var(--color-electric-blue);
  
  /* Background Colors */
  --color-bg-primary: var(--color-secondary);
  --color-bg-secondary: var(--color-gray-50);
  --color-bg-accent: var(--color-primary);
  --color-bg-muted: var(--color-gray-100);
  
  /* Text Colors */
  --color-text-primary: var(--color-primary);
  --color-text-secondary: var(--color-gray-700);
  --color-text-muted: var(--color-gray-600);
  --color-text-inverse: var(--color-secondary);
  --color-text-accent: var(--color-accent);
  
  /* Border Colors */
  --color-border: var(--color-primary);
  --color-border-light: var(--color-gray-300);
  --color-border-accent: var(--color-accent);
  
  /* Shadow Color */
  --color-shadow: var(--color-primary);
  --color-shadow-light: var(--color-gray-400);
  
  /* Typography */
  
  /* Font Families - Bold and Experimental */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-display: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
  
  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Letter Spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
  
  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-base: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
  
  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Typography Classes */
.text-display-1 {
  font-family: var(--font-display);
  font-size: var(--font-size-9xl);
  font-weight: var(--font-weight-black);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tighter);
}

.text-display-2 {
  font-family: var(--font-display);
  font-size: var(--font-size-8xl);
  font-weight: var(--font-weight-extrabold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
}

.text-display-3 {
  font-family: var(--font-display);
  font-size: var(--font-size-7xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
}

.text-h1 {
  font-family: var(--font-display);
  font-size: var(--font-size-6xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
}

.text-h2 {
  font-family: var(--font-display);
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-snug);
}

.text-h3 {
  font-family: var(--font-display);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
}

.text-h4 {
  font-family: var(--font-display);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
}

.text-h5 {
  font-family: var(--font-display);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

.text-h6 {
  font-family: var(--font-display);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

.text-body-lg {
  font-family: var(--font-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
}

.text-body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

.text-body-sm {
  font-family: var(--font-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

.text-caption {
  font-family: var(--font-primary);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

/* Color Utilities */
.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-muted { color: var(--color-text-muted); }
.text-inverse { color: var(--color-text-inverse); }
.text-accent { color: var(--color-text-accent); }

.bg-primary { background-color: var(--color-bg-primary); }
.bg-secondary { background-color: var(--color-bg-secondary); }
.bg-accent { background-color: var(--color-bg-accent); }
.bg-muted { background-color: var(--color-bg-muted); }

/* Responsive Typography */
@media (max-width: 768px) {
  .text-display-1 { font-size: var(--font-size-6xl); }
  .text-display-2 { font-size: var(--font-size-5xl); }
  .text-display-3 { font-size: var(--font-size-4xl); }
  .text-h1 { font-size: var(--font-size-4xl); }
  .text-h2 { font-size: var(--font-size-3xl); }
  .text-h3 { font-size: var(--font-size-2xl); }
}
