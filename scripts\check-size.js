import fs from 'fs';
import path from 'path';

function getDirectorySize(dirPath) {
  let totalSize = 0;
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        totalSize += getDirectorySize(itemPath);
      } else {
        totalSize += stats.size;
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dirPath}:`, error.message);
  }
  
  return totalSize;
}

function formatSize(bytes) {
  const mb = bytes / (1024 * 1024);
  return mb.toFixed(2) + ' MB';
}

console.log('📊 Project Size Analysis (excluding node_modules)');
console.log('================================================');

const directories = ['src', 'public', 'scripts'];
let totalSize = 0;

// Check each directory
directories.forEach(dir => {
  if (fs.existsSync(dir)) {
    const size = getDirectorySize(dir);
    totalSize += size;
    console.log(`${dir}/: ${formatSize(size)}`);
  }
});

// Check root files
const rootFiles = fs.readdirSync('.').filter(item => {
  const stats = fs.statSync(item);
  return stats.isFile();
});

let rootSize = 0;
rootFiles.forEach(file => {
  const stats = fs.statSync(file);
  rootSize += stats.size;
});

totalSize += rootSize;
console.log(`Root files: ${formatSize(rootSize)}`);

console.log('================================================');
console.log(`Total project size: ${formatSize(totalSize)}`);

if (totalSize < 100 * 1024 * 1024) {
  console.log('✅ Project size is under 100MB - ready for GitHub!');
} else {
  console.log('❌ Project size exceeds 100MB - needs more optimization');
}

// Breakdown by file type
console.log('\n📋 File Type Breakdown:');
const fileTypes = {};

function analyzeDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        analyzeDirectory(itemPath);
      } else {
        const ext = path.extname(item).toLowerCase() || 'no-extension';
        if (!fileTypes[ext]) {
          fileTypes[ext] = { count: 0, size: 0 };
        }
        fileTypes[ext].count++;
        fileTypes[ext].size += stats.size;
      }
    }
  } catch (error) {
    // Ignore errors
  }
}

// Analyze all directories and root files
directories.forEach(dir => {
  if (fs.existsSync(dir)) {
    analyzeDirectory(dir);
  }
});

rootFiles.forEach(file => {
  const stats = fs.statSync(file);
  const ext = path.extname(file).toLowerCase() || 'no-extension';
  if (!fileTypes[ext]) {
    fileTypes[ext] = { count: 0, size: 0 };
  }
  fileTypes[ext].count++;
  fileTypes[ext].size += stats.size;
});

// Sort by size
const sortedTypes = Object.entries(fileTypes)
  .sort(([,a], [,b]) => b.size - a.size)
  .slice(0, 10); // Top 10

sortedTypes.forEach(([ext, data]) => {
  console.log(`${ext}: ${data.count} files, ${formatSize(data.size)}`);
});
