/**
 * WhatsApp Integration Utilities
 * Handles WhatsApp API integration for contact forms
 */

const WHATSAPP_NUMBER = '+94715768552';

/**
 * Format phone number for WhatsApp API
 * @param {string} number - Phone number
 * @returns {string} - Formatted number
 */
const formatPhoneNumber = (number) => {
  // Remove all non-digit characters
  const cleaned = number.replace(/\D/g, '');
  
  // Add country code if not present
  if (!cleaned.startsWith('94')) {
    return `94${cleaned}`;
  }
  
  return cleaned;
};

/**
 * Create WhatsApp message from form data
 * @param {Object} formData - Form data object
 * @returns {string} - Formatted message
 */
const createWhatsAppMessage = (formData) => {
  const { name, email, message } = formData;

  let whatsappMessage = `🚀 *New Project Inquiry from Tera Works Website*\n\n`;

  if (name) {
    whatsappMessage += `👤 *Name:* ${name}\n`;
  }

  if (email) {
    whatsappMessage += `📧 *Email:* ${email}\n`;
  }

  whatsappMessage += `\n📝 *Message:*\n${message || 'No additional message provided.'}\n\n`;
  whatsappMessage += `⏰ *Sent:* ${new Date().toLocaleString()}\n`;
  whatsappMessage += `🌐 *Source:* Tera Works Portfolio Website`;

  return whatsappMessage;
};

/**
 * Send message via WhatsApp Web
 * @param {Object} formData - Form data object
 * @returns {string} - WhatsApp URL
 */
export const sendToWhatsApp = (formData) => {
  const phoneNumber = formatPhoneNumber(WHATSAPP_NUMBER);
  const message = createWhatsAppMessage(formData);
  const encodedMessage = encodeURIComponent(message);
  
  const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
  
  // Open WhatsApp in new tab
  window.open(whatsappUrl, '_blank');
  
  return whatsappUrl;
};

/**
 * Send quick message to WhatsApp
 * @param {string} message - Quick message text
 * @returns {string} - WhatsApp URL
 */
export const sendQuickMessage = (message) => {
  const phoneNumber = formatPhoneNumber(WHATSAPP_NUMBER);
  const formattedMessage = `🚀 *Quick Message from Tera Works Website*\n\n${message}\n\n⏰ *Sent:* ${new Date().toLocaleString()}`;
  const encodedMessage = encodeURIComponent(formattedMessage);
  
  const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
  
  // Open WhatsApp in new tab
  window.open(whatsappUrl, '_blank');
  
  return whatsappUrl;
};

/**
 * Validate form data before sending
 * @param {Object} formData - Form data object
 * @returns {Object} - Validation result
 */
export const validateFormData = (formData) => {
  const errors = {};
  
  if (!formData.name || formData.name.trim().length < 2) {
    errors.name = 'Name must be at least 2 characters long';
  }
  
  if (!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = 'Please enter a valid email address';
  }
  
  if (!formData.message || formData.message.trim().length < 10) {
    errors.message = 'Message must be at least 10 characters long';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Get WhatsApp contact info
 * @returns {Object} - Contact information
 */
export const getWhatsAppInfo = () => {
  return {
    number: WHATSAPP_NUMBER,
    displayNumber: '+94 71 576 8552',
    businessHours: '9:00 AM - 6:00 PM (GMT+5:30)',
    responseTime: 'Usually responds within 1 hour',
    availability: 'Monday - Saturday'
  };
};

export default {
  sendToWhatsApp,
  sendQuickMessage,
  validateFormData,
  getWhatsAppInfo
};
